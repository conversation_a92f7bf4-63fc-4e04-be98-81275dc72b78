import {request} from "@/api/sdk";

let debug = {}
if (process.env.NODE_ENV === 'development') {
    debug = require('./debug.js');
}

export default {
    data() {
        return {
            isDebug: false,
            isCheck: false, //是否重复性校验通过标识
            isPeople: false, //是否联系人校验通过标识
            isContactPeople: false, //商客联系号码校验通过标识
            ocrData: {},
            ocrIdData: {},
            checkData: {},
            peopleList: [],
            isOcr: false,
            subData: {
                linkmanPhone: '',
                custName: '',
                idIccId: '',
                idAddress: '',  //集团证件地址
                idValiddate: '',  //集团证件有效期

                idType: '',  //集团证件类型
                idTypeName: '', //集团证件类型名称
                unitLeader: '', //法人名称
                productType: '00', //集团子类型
                productTypeName: '法人', //集团子类型名称
                areaType: '', //经营区域类型
                tradeCode: '', //行业类型
                industryId: '', //行业类别
                micBusiness: '0', //是否小微企业
                operatorCustId: '', //经办人客户ID
                externAddress: '', //客户补充地址
                custLevel: '4', //客户服务等级
                location: '',  //所在地市
                regManaDepartment: '',  // 登记管理部门
                busiCompanyId: '',  // 生产经营所在省编码
                busiLocation: '',  // 生产经营所在地市编码
                busiCountyId: '',  // 生产经营所在区县编码
                busiCountyName: '',  // 生产经营所在区县名称
                busiContactPhone: '',  // 商客联系号码
                level: 'D', //集团等级
                fileName: '', //附件名称
                fileAlias: '',  //系统附件名字
                annualSales: '50',  //营业额
                staffLogin: '',  //客户经理工号
                staffLoginName: '',   //客户经理姓名
                fileBase64: '',
                entType: '17',  //企业类型
                employeesNum: '',  //
                memberCount: '',  //企业员工数
                smallGrpFlag: '0',  //中小微企业标志
                label: '',//营销场景码值
                lon: '',
                lat: '',
                cmiotSynFlag: '',  //是否同步CMIOT
                exportImportFlag: '',  //是否进出口客户
                riskApplyId: '',  //风险评估单编号
                custId: '',
                custAccept: '',
                unitId: '',
                riskApplyInfo:[]
            },
            fxpgdData: {},
            fxpgdShow: false,
            fxpgd: '',
            lves: [
                {
                    "CODE_NAME": "A类集团",
                    "CODE_VALUE": "A"
                },
                {
                    "CODE_NAME": "B类集团",
                    "CODE_VALUE": "B"
                },
                {
                    "CODE_NAME": "C类集团",
                    "CODE_VALUE": "C"
                },
                {
                    "CODE_NAME": "D类集团",
                    "CODE_VALUE": "D"
                }],
            micList: [
                {
                    "CODE_NAME": "其他",
                    "CODE_VALUE": "0"
                },
                {
                    "CODE_NAME": "中小微企业",
                    "CODE_VALUE": "1"
                },
                {
                    "CODE_NAME": "类中小微企业",
                    "CODE_VALUE": "2"
                }],
            codeList: {
                d38952: [],
                d11113: [],
                d38588: [],
                d38954: [],
                d38953: [],
                d38513: [],
                d38442: [],
                d38585: [],
                d38584: [],
                d38587: [],
                d38586: [],
                d38592: [],
                d385811: [],
                d38583: [],
                d38582: [],
                d385814: [],
                d385810: [],
                d385203: [],
                d38510: [],
                d385836: [],
                regManaDepartment: []
            },
            d385110: [
                {
                    "CODE_VALUE": "8",
                    "VERIFIC_FLAG": "Y",
                    "CODE_NAME": "营业执照号码",
                    "ORC_FLAG": "Y"
                },
                {
                    "CODE_VALUE": "20",
                    "VERIFIC_FLAG": "Y",
                    "CODE_NAME": "统一社会信用代码证书",
                    "ORC_FLAG": "Y"
                },
                {
                    "CODE_VALUE": "9",
                    "VERIFIC_FLAG": "Y",
                    "CODE_NAME": "组织机构代码证",
                    "ORC_FLAG": "Y"
                },
                {
                    "CODE_VALUE": "10",
                    "VERIFIC_FLAG": "Y",
                    "CODE_NAME": "事业单位法人证书",
                    "ORC_FLAG": "Y"
                },
                {
                    "CODE_VALUE": "11",
                    "VERIFIC_FLAG": "N",
                    "CODE_NAME": "单位介绍信",
                    "ORC_FLAG": "N"
                },
                {
                    "CODE_VALUE": "18",
                    "VERIFIC_FLAG": "N",
                    "CODE_NAME": "军队代码",
                    "ORC_FLAG": "N"
                },
                {
                    "CODE_VALUE": "19",
                    "VERIFIC_FLAG": "N",
                    "CODE_NAME": "有偿服务许可证",
                    "ORC_FLAG": "N"
                },
                {
                    "CODE_VALUE": "12",
                    "VERIFIC_FLAG": "Y",
                    "CODE_NAME": "社会团体法人登记证书",
                    "ORC_FLAG": "N"
                },
                {
                    "CODE_VALUE": "21",
                    "VERIFIC_FLAG": "Y",
                    "CODE_NAME": "宗教活动场所登记证",
                    "ORC_FLAG": "N"
                },
                {
                    "CODE_VALUE": "22",
                    "VERIFIC_FLAG": "Y",
                    "CODE_NAME": "民办非企业单位登记证书",
                    "ORC_FLAG": "N"
                },
                {
                    "CODE_VALUE": "23",
                    "VERIFIC_FLAG": "Y",
                    "CODE_NAME": "基金会法人登记证书",
                    "ORC_FLAG": "N"
                },
                {
                    "CODE_VALUE": "24",
                    "VERIFIC_FLAG": "Y",
                    "CODE_NAME": "律师事务所执业许可证",
                    "ORC_FLAG": "N"
                },
                // {
                //     "CODE_VALUE": "17",
                //     "VERIFIC_FLAG": "N",
                //     "CODE_NAME": "身份证和店铺门头照",
                //     "ORC_FLAG": "Y"
                // }
            ],
            d385203_1: [],
            d385203_2: [],
            d38747: [],
            d385111: [],
            levelOp: [
                {
                    "CODE_NAME": "是",
                    "CODE_VALUE": "1"
                },
                {
                    "CODE_NAME": "否",
                    "CODE_VALUE": "2"
                }
            ],
            synFlaglist: [
                {

                    "CODE_NAME": "否",
                    "CODE_VALUE": "N"
                },
                {
                    "CODE_NAME": "是",
                    "CODE_VALUE": "Y"
                }
            ],
            importFlag: [
                {
                    "CODE_NAME": "否",
                    "CODE_VALUE": "N"
                },
                {
                    "CODE_NAME": "是",
                    "CODE_VALUE": "Y"
                }
            ],
            regManaDepartment: [
                {
                    "CODE_NAME": "机构编制",
                    "CODE_VALUE": "机构编制"
                },
                {
                    "CODE_NAME": "外交",
                    "CODE_VALUE": "外交"
                },
                {
                    "CODE_NAME": "司法行政",
                    "CODE_VALUE": "司法行政"
                },
                {
                    "CODE_NAME": "文化",
                    "CODE_VALUE": "文化"
                },
                {
                    "CODE_NAME": "民政",
                    "CODE_VALUE": "民政"
                },
                {
                    "CODE_NAME": "旅游",
                    "CODE_VALUE": "旅游"
                },
                {
                    "CODE_NAME": "宗教",
                    "CODE_VALUE": "宗教"
                },
                {
                    "CODE_NAME": "工会",
                    "CODE_VALUE": "工会"
                },
                {
                    "CODE_NAME": "工商",
                    "CODE_VALUE": "工商"
                },
                {
                    "CODE_NAME": "中央军委改革和编制办公室",
                    "CODE_VALUE": "中央军委改革和编制办公室"
                },
                {
                    "CODE_NAME": "农业",
                    "CODE_VALUE": "农业"
                },
                {
                    "CODE_NAME": "其他",
                    "CODE_VALUE": "其他"
                }
            ],
            isindustryInfo: [],
            ROUTE_LIST: [],
            applyData: {
                nextTacheInfo: {
                    "ROUTE_ID": '1',
                    "START_FLAG": 'D',
                    "NEXT_TACHE_ID": '',
                    "NEXT_TACHE_NAME": '',
                    "ROLE_ID": '',
                    "ALL_CITY_FLAG": '',
                    "STAGE_CODE": '',
                    "LIMIT_TIME": '',
                    "END_FLAG": '',
                    "DEAL_TYPE": '',
                    "MODEL_NAME": '',
                    "MODEL_ID": '',
                    "TACHE_ID": 'ABLJTDJJD_APPLY_01',
                    "REPEAT_FLAG": '',
                    "BUSI_REQ_TYPE": '',
                    "HIDE_FLAG": '',
                    "LOGIN_NO": '',
                    "LOGIN_NAME": '',
                    "LOGIN_PHONE": '',
                    "REGION_ID": '',
                    "NEXT_DEAL_NO": '',
                    "NEXT_DEAL_NAME": '',
                    "NEXT_DEAL_PHONE": '',
                },
                applyId: '',
                busiId: '',
                applyNotes: '',
                operPhone: '',
                orderId: '',
                unitAttact: '',
                custlevel: '',
                loginGroup: '',
                unitName: '',
                unitId: '',
                custId: '',
                fileName: '',
                fileAlias: '',
                attachType: '',

            },
            routeList: [],
            nextLoginNoList: [],
        }
    },
    methods: {
        swMsg(flag) {
            switch (flag) {
                case '1':
                    return "证件号码已存在"
                case '3':
                    return "证件号码未传入"
                case '4':
                    return "证件号码中存在小写字母"
                case '6':
                    return "证件号码存在特殊字符"
                case '21':
                    return "集团名称未输入"
                case '22':
                    return "集团名称已存在"
                case '23':
                    return "客户名称存在特殊字符"
                case '24':
                    return "客户名称存在阿拉伯数字"
                case '25':
                    return "客户名称至少4个汉字"
                default:
                    return "验真失败"
            }
        },
        pubGetSeq(opdata) {
            let op = {
                name: 'addGroup.pubGetSeq',
                data: opdata,
                isDebug: this.isDebug,
                debugData: debug.cmiot,
            }
            this.$request(op).then((res) => {
                if (opdata.type === '7') {
                    this.subData.custId = res.data
                } else {
                    this.subData.custAccept = res.data

                }
            });
        },
        qryGrpCustUnitId() {
            let op = {
                name: 'addGroup.qryGrpCustUnitId',
                data: {},
                isDebug: this.isDebug,
                debugData: debug.cmiot,
            }
            this.$request(op).then((res) => {
                this.subData.unitId = res.data
                this.pubGetSeq({type:'7'})  //获取CUST_ID
                this.pubGetSeq({type:'6'})  //获取CustAccept
            });
        },
        qryCmiotCustManagerByLoginNo(item) {
            let op = {
                name: 'addGroup.qryCmiotCustManagerByLoginNo',
                data: {loginNo: item},
                isDebug: this.isDebug,
                debugData: debug.cmiot,
            }
            this.$request(op).then((res) => {
                this.subData.cmiotSynFlag = 'N'
                this.importFlagShow = false
                this.subData.exportImportFlag = ''

                if (res.data.CMIOT_CUSTMGR_LIST.length != 0) {
                    this.cmiotClickable = true
                } else {
                    this.cmiotClickable = false

                }
            });
        },
        /**
         * 生成iccid
         */

        createGrpCustIccId(idType) {
            let op = {
                name: 'addGroup.createGrpCustIccId',
                data: {idType},
                isDebug: this.isDebug,
                debugData: debug.iccid,
            }
            this.$request(op).then((res) => {
                console.log(res.data)
                this.subData.idIccId = res.data
            });
        },
        //级联查询查询行业类别
        dynSqlQryTrade(tradeCode) {
            if (this.isindustryInfo.length > 0) {
                this.d38747 = this.codeList.d38582.find(i => tradeCode === i.BU_INDUSTRY_ID).BU_INDUSTRY_SUB_INFO
            } else {
                const op = {
                    isDebug: this.isDebug,
                    name: 'addGroup.dynSqlQry',
                    debugData: debug.reqData,
                    data: {
                        tradeCode
                    }
                }
                this.$request(op).then((res) => {
                    this.d38747 = res.data;
                });
            }

        },
        /**
         * 级联查询
         */
        dynSqlQryCounty(parentCode, level, defaultName) {
            let op = {
                name: 'addGroup.dynSqlQryCounty',
                data: {parentCode},
                isDebug: this.isDebug,
                debugData: debug.city,
            }
            // if (level === 1){
            //     await this.codeList.d385203_1 = this.$request(op).data.ROW
            // }
            // if (level === 2){
            //     await this.codeList.d385203_2 = this.$request(op).data.ROW
            // }
            this.$request(op).then((res) => {
                console.log(parentCode, level)
                if (level === 1) {
                    this.d385203_1 = res.data
                    if (defaultName) {
                        this.subData.busiLocation = this.d385203_1.find(i => {
                            return i.CODE_NAME == defaultName
                        }).CODE_VALUE
                    }
                }
                if (level === 2) {
                    this.d385203_2 = res.data
                    if (defaultName) {
                        this.subData.busiLocation = this.d385203_2.find(i => {
                            return i.CODE_NAME == defaultName
                        }).CODE_VALUE
                    }
                }
            });
        },
        /**
         * 工号归属备案区县信息
         */
        qryLoginBelongCounty() {
            let op = {
                name: 'addGroup.qryLoginBelongCounty',
                data: {},
                isDebug: this.isDebug,
                debugData: debug.code,
            }
            this.$request(op).then((res) => {
                // this.regManaDepartment = res.data.
                this.subData.busiCompanyId = res.data.BUSI_COMPANY_ID

                this.subData.busiLocation = res.data.BUSI_LOCATION
                this.subData.busiCountyId = res.data.BUSI_COUNTY_ID
                this.subData.busiCountyName = res.data.BUSI_COUNTY_NAME

                this.dynSqlQryCounty(this.subData.busiLocation, 1)
                this.dynSqlQryCounty(this.subData.busiCountyId, 2)
            });
        },
        /**
         * 状态校验
         * @param idIccId
         * @param idType
         * 20231016 屏蔽
         */
        // qryGrpCustStatus(idIccId, idType) {
        //     let op = {
        //         name: 'addGroup.qryGrpCustStatus',
        //         data: {
        //             idIccId,
        //             idType
        //         }
        //     }
        //     this.$request(op).then((res) => {
        //         this.$toast("校验通过")
        //         this.isCheck = true
        //         if (res.data.BLACK_FLAG == 'Y') {
        //             Dialog.alert({
        //                 title: '提示',
        //                 message: '该客户为高风险客户，请谨慎发展业务',
        //             }).then(() => {
        //                 // on close
        //             });
        //         }
        //     });
        // },
        /**
         * 重复性校验
         * @param custName 集团名称
         * @param idIccId
         * @param idType
         */
        checkGrpCustIccId(custName, idIccId, idType) {
            let op = {
                name: 'addGroup.checkGrpCustIccId',
                data: {
                    custName,
                    idIccId,
                    idType
                }
            }
            this.$request(op).then((res) => {
                if (res.data.CHECK_COUNT == '0') {
                    this.isCheck = true
                } else {
                    this.isCheck = false
                    this.$toast(this.swMsg(res.data.CHECK_COUNT))

                }
            });
        },
        /**
         * 新建集团提交
         * @param sub
         */
        approveGrpCustInfo(sub) {
            let op = {
                name: 'addGroup.approveGrpCustInfoNew',
                data: sub
            }
            this.$request(op).then((res) => {
                this.unitId = res.data.UNIT_ID
                this.custId = res.data.CUST_ID
                if (this.BEGIN_VALUE != 'C' && this.BEGIN_VALUE != 'D') {
                    this.applyData.fileName = res.data.fileName
                    this.applyData.fileAlias = res.data.fileAlias
                    switch (this.subData.idType) {
                        case '8':  //营业执照
                            this.applyData.attachType = '105'
                            break
                        case '20':  //统一社会信用代码证书
                            this.applyData.attachType = '165'
                            break
                        case '9':  //组织机构代码
                            this.applyData.attachType = '166'
                            break
                        case '10':  //事业单位法人证书
                            this.applyData.attachType = '167'
                            break
                        case '11':  //单位介绍信
                            this.applyData.attachType = '171'
                            break
                        case '18':  //军队代码
                            this.applyData.attachType = '172'
                            break
                        case '19':  //有偿服务许可证
                            this.applyData.attachType = '173'
                            break
                        case '12':  //社会团体法人登记证
                            this.applyData.attachType = '174'
                            break
                        case '21':  //宗教活动场所等级证
                            this.applyData.attachType = '175'
                            break
                        case '22':  //民办非企业单位登记证
                            this.applyData.attachType = '176'
                            break
                        case '23':  //基金会法人登记证
                            this.applyData.attachType = '177'
                            break
                        case '24':  //律师事务所执业许可证
                            this.applyData.attachType = '179'
                            break
                        case '17':  //身份证和店铺门头照
                            this.applyData.attachType = '170'
                            break
                        default:
                            this.applyData.attachType = '115'
                            break
                    }
                    this.busiApplySub()

                } else {
                    if (this.__query("flag") === '1' || this.__query("type") != null) {
                        window.location.href = `http://*************:8090/shop/#/taskMan/JTInfo?unitId=${this.unitId}&custId=${this.custId}&name=${sub.custName}`
                    } else {
                        this.showSuccess = true
                    }
                }

            });
        },
        /**
         * 下拉字典
         * @param contactPhoneNo 手机号
         */
        dynSqlQry() {
            let op = {
                name: 'addGroup.dynSqlQryall',
                isDebug: this.isDebug,
                debugData: debug.xlData,
                data: {}
            }
            this.$request(op).then((res) => {
                this.codeList = res.data;
                this.subData.location = this.codeList.d385810.find(i => {
                    return i.CODE_NAME === this.codeList.groupName
                }).CODE_VALUE
            });
        },
        /**
         * 下拉字典
         * @param contactPhoneNo 手机号
         */
        basemngDynSqlQryall() {
            let op = {
                name: 'addGroup.basemngDynSqlQryall',
                isDebug: this.isDebug,
                debugData: debug.xlData,
                data: {}
            }
            this.$request(op).then((res) => {
                this.d385111 = res.data.d385111
            });
        },
        /**
         * 检验联系人电话
         * @param contactPhoneNo 手机号
         */
        contactPhoneNo(contactPhoneNo) {
            let op = {
                name: 'addGroup.contactPhoneNo',
                isDebug: this.isDebug,
                debugData: debug.phoneData,
                data: {
                    contactPhoneNo
                }
            }
            this.$request(op).then((res) => {
                if (res.data.RESULT_FLAG == '0') {
                    this.isPeople = true
                    this.$toast('验证通过')
                    this.subData.operatorCustId = res.data.CONTACT_CUST_ID;
                } else {
                    this.isPeople = false
                    this.subData.operatorCustId = '';
                    this.$toast(res.data.RESULT_MSG)
                }

            });
        },
        /**
         * 检验商客联系号码
         * @param contactPhoneNo 手机号
         */
        contactPhoneNo2(contactPhoneNo) {
            let op = {
                name: 'addGroup.contactPhoneNo',
                isDebug: this.isDebug,
                debugData: debug.phoneData,
                data: {
                    contactPhoneNo
                }
            }
            this.$request(op).then((res) => {
                if (res.data.RESULT_FLAG == '0') {
                    this.isContactPeople = true
                    this.$toast('验证通过')
                } else {
                    this.isContactPeople = false
                    this.$toast(res.data.RESULT_MSG)
                }
            });
        },
        /**
         * ocrs识别
         * @param fileBase64
         * @param idType //营业执照-8；组织机构代码证-9；事业单位证书-10；统一社会代码证-20
         */
        queryOcr(fileBase64, idType) {
            const op = {
                name: 'addGroup.identificInfo',
                isDebug: this.isDebug,
                debugData: debug.reqData,
                data: {
                    fileBase64,
                    idType
                }
            }
            this.$request(op).then((res) => {
                if (res.data.RESULT_FLAG == '0') {
                    this.ocrData = res.data
                    this.ocrShow = true
                    // this.subData.fileName = this.checkData.ENT_NAME, //附件名称
                    this.subData.fileAlias = this.ocrData.FILE_ALIAS //系统附件名字
                } else {
                    this.$toast(res.data.RESULT_NOTE)
                }


                // if (this.ocrData.OP_TO === '长期') {
                //     this.ocrData.OP_TO = '20991231'
                // }
            });
        },

        /**
         * 自用OCR识别
         * @param data - 包含image(base64图片)和flag(证件类型)的对象
         */
        callOcrApi(data) {
            // 根据证件类型设置不同的坐标参数
            let tableCoord = {};

            if (data.flag === '24') {
                tableCoord = {
                    "集团名称": [127, 251, 432, 297],
                    "信用代码": [614, 190, 809, 229]
                };
            } else if (data.flag === '12') {
                tableCoord = {
                    "集团名称": [291, 334, 594, 394],
                    "法人": [769, 352, 935, 403],
                    "地址": [90, 487, 627, 565]
                };
            } else if (data.flag === '11') {
                tableCoord = {
                    "集团名称": [95, 211, 830, 259],
                    "法人": [553, 271, 706, 317]
                };
            } else if (data.flag === '23') {
                tableCoord = {
                    "集团名称": [278, 331, 564, 377],
                    "法人": [707, 332, 848, 377],
                    "地址": [282, 382, 568, 425]
                };
            }

            const requestData = {
                image: data.image,
                flag: data.flag
            };

            const op = {
                name: 'system.ocr.recognize',
                // isDebug: this.isDebug,
                debugData: debug.reqData,
                data: requestData
            }

            this.$request(op).then((res) => {
                console.log('OCR识别结果:', res);

                if (res.status === 200 && res.data && res.data.length > 0 && res.data[0].result && res.data[0].result.length > 0) {
                    const ocrResult = res.data[0].result[0];

                    // 根据识别结果填充表单字段
                    if (ocrResult["集团名称"]) {
                        this.subData.custName = ocrResult["集团名称"];
                    }
                    if (ocrResult["法人"]) {
                        this.subData.unitLeader = ocrResult["法人"];
                    }
                    if (ocrResult["地址"]) {
                        this.subData.idAddress = ocrResult["地址"];
                    }
                    if (ocrResult["信用代码"]) {
                        this.subData.idIccId = ocrResult["信用代码"];
                    }

                    this.$toast('OCR识别成功');
                } else {
                    // 处理识别失败的情况
                    console.error("OCR识别失败或未获取到有效结果");
                    this.$toast('OCR识别失败，请手动输入');
                }
            }).catch((error) => {
                console.error('OCR API调用错误:', error);
                this.$toast('OCR识别服务异常，请手动输入');
            });
        },

        /**
         * ocrs识别,身份证
         * @param zjzzm
         * @param zjzfm
         * @param mtz
         */
        identificLicensePerson(zjzzm, zjzfm, mtz) {
            const op = {
                name: 'addGroup.identificLicensePerson',
                isDebug: this.isDebug,
                debugData: debug.idcard,
                data: {
                    zjzzm,
                    zjzfm,
                    mtz
                }
            }
            this.$request(op).then((res) => {
                if (res.data.RESULT_FLAG == '0') {
                    this.ocrIdData = res.data
                    this.ocrShow = true
                    // this.subData.fileName = this.checkData.ENT_NAME, //附件名称
                    this.subData.fileAlias = this.ocrIdData.SHOP_FILE_ALIAS //系统附件名字
                    this.ocrIdData.VALID_DATE_END = this.ocrIdData.VALID_DATE_END == '长期' ? '20501231' : this.ocrIdData.VALID_DATE_END
                    // if (this.ocrData.OP_TO === '长期') {
                    //     this.ocrData.OP_TO = '20991231'
                    // }
                } else {
                    this.ocrShow = false
                    this.$toast(res.data.RESULT_NOTE)
                }
            });
        },
        /**
         * 企业建档可选政企行业查询接口
         * @param zjzzm
         * @param zjzfm
         * @param mtz
         */
        industryQuery(custName, idIccid, idType) {
            const op = {
                name: 'addGroup.industryQuery',
                isDebug: this.isDebug,
                debugData: debug.industryInfo,
                data: {
                    custName,
                    idIccid,
                    idType,
                }
            }
            this.$request(op).then((res) => {
                if (res.data.INDUSTRY_INFO != undefined && res.data.INDUSTRY_INFO.length > 0) {
                    this.codeList.d38582 = []
                    this.isindustryInfo = res.data.INDUSTRY_INFO
                    this.codeList.d38582 = this.isindustryInfo
                }
                console.log(this.isindustryInfo)
                console.log(this.codeList.d38582)
                if (res.data.RESULT_FLAG != 'Y' || res.data.RESULT_FLAG == undefined) {
                    this.$toast("企业建档可选政企行业查询失败，不允许操作")
                    return
                }
                if (this.ocr.isCheck) {
                    this.grpChkAndEcVerific(this.subData.custName, this.subData.idIccId, this.subData.idType,
                        this.subData.unitLeader)
                } else {
                    this.checkGrpCustIccId(this.subData.custName, this.subData.idIccId, this.subData.idType)
                }
            });
        },
        /**
         * 集团客户校验
         * @param custName 客户名称
         * @param idIccid 证件号码
         * @param idType  证件类型
         * @param legalPersonName 法人姓名
         */
        grpChkAndEcVerific(custName, idIccid, idType, legalPersonName) {
            const op = {
                name: 'addGroup.grpChkAndEcVerific',
                isDebug: this.isDebug,
                debugData: debug.checkData,
                data: {
                    custName,
                    idIccid,
                    idType,
                    legalPersonName
                }
            }
            this.$request(op).then((res) => {
                if (res.data.RESULT_FLAG == '0') {
                    this.isCheck = true
                    if (res.data.BLACK_FLAG == 'Y') {
                        // Dialog.alert({
                        //     title: '提示',
                        //     message: '该客户为高风险客户，请谨慎发展业务',
                        // }).then(() => {
                        //     // on close
                        // });
                        this.$toast("该客户不允许在网格通建档")
                        return
                    }
                    if (res.data.BLACK_FLAG == 'N') {
                        this.$toast("校验通过")
                    }
                    this.checkData = res.data.EC_DETAIL_INFO
                    this.subData.linkmanName = ''
                    this.subData.custName = this.checkData.ENT_NAME
                    this.subData.idIccId = this.checkData.UNISC_ID
                    this.subData.idAddress = this.checkData.ADDRESS //集团证件地址groupChange.dynSqlQry
                    this.subData.idValiddate = this.checkData.OP_TO == '长期' ? '20501231' : this.checkData.OP_TO //集团证件有效期
                    this.subData.unitLeader = this.checkData.NAME //法人名称
                    // this.subData.productType = this.checkData.ENT_NAME, //集团子类型
                    // this.subData.productTypeName = this.checkData.ENT_NAME, //集团子类型名称
                    // this.subData.areaType = this.checkData.OPS_SCOPE //经营区域类型
                    this.subData.annualSales = this.checkData.ANNUAL_SALES ? this.checkData.ANNUAL_SALES : '50' // 注册资本
                    // if (isHaveCN(this.checkData.INDUSTRY)){
                    //     this.subData.tradeCode = this.codeList.d38582.find( i => {return i.CODE_NAME == this.checkData.INDUSTRY}).CODE_VALUE
                    // }
                    // if (isHaveCN(this.checkData.INDUSTRY_SUB)){
                    //     this.subData.industryId = this.codeList.d38514.find( i => {return i.CODE_NAME == this.checkData.INDUSTRY}).CODE_VALUE
                    // }

                    if (this.isindustryInfo.length > 0) {
                        let indinfo = this.codeList.d38582.find(i => this.checkData.INDUSTRY === i.BU_INDUSTRY_ID)
                        if (indinfo) {
                            this.subData.tradeCode = this.checkData.INDUSTRY
                            let indsubinfo = indinfo.find(i => this.checkData.INDUSTRY_SUB === i.BU_INDUSTRY_SUB_ID).BU_INDUSTRY_SUB_INFO
                            if (indsubinfo) {
                                this.subData.industryId = this.checkData.INDUSTRY_SUB //行业类别
                                this.d38747 = indsubinfo
                            }
                        }

                    } else {
                        this.subData.tradeCode = this.checkData.INDUSTRY //行业类型
                        this.subData.industryId = this.checkData.INDUSTRY_SUB //行业类别
                    }

                    if (this.checkData.UPDATE_INDUSTRY_FLAG === '1') {
                        this.isindustry = true
                        this.isindustrysub = true
                    } else if (this.checkData.UPDATE_INDUSTRY_FLAG === '0') {
                        this.isindustry = false
                        this.isindustrysub = false
                    } else if (this.checkData.UPDATE_INDUSTRY_FLAG === '2') {
                        this.isindustry = false
                        this.isindustrysub = true
                    }
                    this.dynSqlQryTrade(this.subData.tradeCode)
                    // this.subData.operatorCustId = this.checkData.ENT_NAME, //经办人客户ID
                    this.subData.externAddress = this.checkData.ADDRESS //客户补充地址

                    this.subData.regManaDepartment = this.checkData.REG_MANA_DEPARTMENT  // 登记管理部门

                    this.subData.busiCompanyId = this.codeList.d385203.find(i => {
                        return i.CODE_NAME == this.checkData.REGISTERED_PROVINC
                    }).CODE_VALUE

                    // this.subData.busiCompanyId = this.checkData.REGISTERED_PROVINCE  // 生产经营所在省编码
                    // this.subData.busiLocation = this.checkData.REGISTERED_CITY  // 生产经营所在地市编码
                    // this.subData.busiCountyId = this.checkData.REGISTERED_COUNTRY  // 生产经营所在区县编码
                    this.subData.busiCountyName = this.checkData.ENT_NAME  // 生产经营所在区县名称
                    this.dynSqlQryCounty(this.subData.busiLocation, 1, this.checkData.REGISTERED_CITY)
                    this.dynSqlQryCounty(this.subData.busiCountyId, 2, this.checkData.REGISTERED_COUNTRY)
                    this.subData.memberCount = this.checkData.EMP_NUM;  // 员工数

                } else {
                    this.isCheck = false
                    this.$toast(res.data.RESULT_NOTE)
                }
            });
        },
        /**
         * 查询地市客户经理
         * @param loginNo  支持模糊查询
         * @param loginName  支持模糊查询
         * @param custLevel 客户等级
         * @param pageNum   页数
         */
        qryManagerLoginInRegion(queryParams) {
            this.loading = true;
            let op = {
                isDebug: this.isDebug,
                debugData: debug.peopleData,
                name: 'addGroup.qryManagerLoginInRegion',
                data: queryParams
            }
            this.$request(op).then((res) => {
                if (queryParams.flag == '1') {
                    if (res.data.MANAGER_LIST.length > 0) {
                        this.subData.staffLoginName = res.data.MANAGER_LIST[0].LOGIN_NAME
                        this.subData.staffLogin = res.data.MANAGER_LIST[0].LOGIN_NO
                    } else {
                        this.subData.staffLoginName = ''
                        this.subData.staffLogin = ''
                    }
                } else {
                    this.peopleShow = true
                    this.total = res.data.COUNT_NUM;
                    this.peopleList.push(...res.data.MANAGER_LIST);
                    this.loading = false;
                    if (res.data.COUNT_NUM <= this.peopleList.length) {
                        this.finished = true
                    }
                }

            });
        },

        qryIdentificEnterprise() {
            this.loading = true;
            let op = {
                isDebug: this.isDebug,
                debugData: debug.peopleData,
                name: 'smallGrpFlagChange.qryIdentificEnterprise',
                data: {
                    custName: this.subData.custName,
                    industryId: this.subData.industryId,
                    entTypeId: this.subData.entType,
                    memberCount: this.subData.memberCount,
                    annualSales: this.subData.annualSales,

                }
            }
            this.$request(op).then((res) => {
                this.subData.smallGrpFlag = res.data.SMALL_GRP_FLAG;  //中小微企业标识
                if (res.data.SMALL_GRP_FLAG == '') {
                    this.$toast("鉴定无结果，请选择中小微企业类型")
                    this.clickIDTYPE = true
                    return
                }
                if (res.data.SMALL_GRP_FLAG === '1' || res.data.SMALL_GRP_FLAG === '2') {
                    this.subData.micBusiness = '1'
                    this.$toast("尊敬的客户，经校验您为中小微企业客户，为响应国家对中小微企业宽带和互联网专线降费的号召，" +
                        "企业宽带、互联网专线可享受10%优惠。")
                } else {

                    this.subData.micBusiness = '0'
                }
                this.loading = false;

            });
        },
        /**
         * 工号权限校验
         *
         * @param imei
         * @param resCode
         */
        async permission(funcCode) {
            const op = {
                name: 'query.permission',
                isDebug: this.isDebug,
                debugData: debug.permission,
                data: {
                    funcCode
                }
            }
            return await request(op);
        },
        /**
         * 风险评估开关
         */

        qryfxpgkg() {
            let op = {
                name: 'addGroup.fxpgkg',
                data: {},
                isDebug: this.isDebug,
                debugData: debug.fxpgkg,
            }
            this.$request(op).then((res) => {
                console.log(res.data)
                this.fxpgkg = res.data
                if (this.fxpgkg == 'Y') {
                    this.qryCtGrpCustRiskAssessInfo({
                        idIccid: this.subData.idIccId,
                        idType: this.subData.idType,
                        qryType:'01'
                    })
                }
            });
        },
        /**
         * 查询风险评估单
         */

        qryCtGrpCustRiskAssessInfo(opdata) {
            let op = {
                name: 'addGroup.qryCtGrpCustRiskAssessInfo',
                data: opdata,
                isDebug: this.isDebug,
                debugData: debug.fxpgdData,
            }
            this.$request(op).then((res) => {
                console.log(res.data)
                if (JSON.stringify(res.data) != '{}') {
                    console.log(res.data.RISK_ASSESS_INFO_LIST.length)
                    if (res.data.RISK_ASSESS_INFO_LIST.length != 0) {  //有风险单
                        // this.fxpgdData = res.data.RISK_ASSESS_INFO_LIST
                        this.fxpgdData = res.data.RISK_ASSESS_INFO_LIST.map(item => ({
                            ...item,
                            checked: false  // 初始化未选中
                        }));
                        this.fxpgdShow = true
                        this.fxpgd = 'Y'
                        this.checkfxpgd(opdata)
                    } else {
                        this.$toast("该证件号码不存在集团客户风险评估申请单，请到“集团客户风险评估”模块进行申请后，再同步CMIOT")
                        return
                    }
                } else {
                    this.$toast("该证件号码不存在集团客户风险评估申请单，请到“集团客户风险评估”模块进行申请后，再同步CMIOT")
                    return
                }
            });
        },
        /**
         * 在途单校验
         */

        checkfxpgd(opdata) {
            let op = {
                name: 'addGroup.getBusiIdByIdiccid',
                data: opdata,
            }
            this.$request(op).then((res) => {
                console.log(res.data)
                console.log(res.data.BUSIID_LIST.length)
                if (res.data.BUSIID_LIST.length > 0) {  //有在途单
                    this.$toast("该证件号码存在在途审批单,不允许关联")
                    return
                } else {
                    this.subData.riskApplyId = this.fxpgd
                    this.$toast("校验通过")

                }
            });
        },

        /**
         * 在途单校验
         */

        qryLoginGroupLevel() {
            let op = {
                name: 'addGroup.qryLoginGroupLevel',
                data: {},
                isDebug: this.isDebug,
                debugData: debug.qryLoginGroupLevel,
            }
            this.$request(op).then((res) => {
                console.log(res.data.GROUP_LEVEL)
                if (res.data.GROUP_LEVEL == '1') {  //工号归属渠道类型  1 地市重客 2 地市
                    this.applyData.loginGroup = 'R2'
                } else {
                    this.applyData.loginGroup = 'R1'
                }
            });
        },
        getSeqByNM(opData) {
            const op = {
                name: 'qly.getSeqByNM',
                isDebug: this.isDebug,
                debugData: debug.reqData,
                data: opData
            }
            this.$request(op).then((res) => {
                this.busiId = res.data
                this.applyId = res.data
                this.applyData.applyId = res.data
                this.applyData.busiId = res.data

            });
        },
        getorderId(opData) {
            const op = {
                name: 'qly.getSeqByNM',
                isDebug: this.isDebug,
                debugData: debug.reqData,
                data: opData
            }
            this.$request(op).then((res) => {
                this.applyData.orderId = res.data

            });
        },
        /**
         * 查询审批人
         */

        getStartRouteInfosByProdId(opdata) {
            let op = {
                name: 'addGroup.getStartRouteInfosByProdId',
                data: {
                    routeFilter: opdata,
                },
                isDebug: this.isDebug,
                debugData: debug.getStartRouteInfosByProdId,
            }
            this.$request(op).then((res) => {
                this.routeList = res.data
                this.routeRadioCheck(this.routeList[0], 0)
            });
        },
        //获取工号号码
        qryLoginMsg() {
            let op = {
                name: 'media.qryLoginMsg',
                isDebug: this.isDebug,
                debugData: debug.d385110,
                data: {}
            }
            this.$request(op).then((res) => {
                this.applyData.operPhone = res.data
            });
        },
        // 查询处理人
        getGroupLoginInfos(roleId) {
            this.nextLoginNoList = []
            this.$request({
                name: 'fxpgd.getGroupLoginInfos',
                data: {
                    orgCode: roleId,
                },
            }).then(res => {
                if (res.data.loginNo == 'aaaa8J') {
                    this.nextLoginNoList.push({
                        "LOGIN_NO": 'aaaa8J',
                        "LOGIN_NAME": '付强',
                        "CONTACT_PHONE": '17703462929'
                    })
                } else if (res.data.loginNo == 'fhhh0M') {
                    this.nextLoginNoList.push({
                        "LOGIN_NO": 'fhhh0M',
                        "LOGIN_NAME": '詹建香',
                        "CONTACT_PHONE": '15234902419'
                    })
                    this.applyData.operPhone = '15234902419'
                } else {
                    this.nextLoginNoList = res.data.LOGIN_NO_LIST.LOGIN_NO_LIST
                }
                let nextRouteInfo = this.routeList[this.routeRadio]
                this.applyData.nextTacheInfo = nextRouteInfo
                this.applyData.nextTacheInfo.NEXT_DEAL_NO = this.nextLoginNoList[this.nextLoginNoRadio].LOGIN_NO
                this.applyData.nextTacheInfo.NEXT_DEAL_NAME = this.nextLoginNoList[this.nextLoginNoRadio].LOGIN_NAME
                this.applyData.nextTacheInfo.NEXT_DEAL_PHONE = this.nextLoginNoList[this.nextLoginNoRadio].CONTACT_PHONE
                this.applyData.nextTacheInfo.LOGIN_NO = res.data.loginNo
                this.applyData.nextTacheInfo.LOGIN_NAME = res.data.name
                this.applyData.nextTacheInfo.REGION_ID = res.data.regionId
                this.applyData.nextTacheInfo.LOGIN_PHONE = this.applyData.operPhone
                this.applyData.nextTacheInfo.NEXT_TACHE_NAME = nextRouteInfo.ROUTE_NAME

            })
        },
        // AB类集团申请审批
        busiApplySub() {
            this.applyData.custId = this.custId
            this.applyData.unitId = this.unitId
            this.applyData.unitName = this.subData.custName
            this.applyData.custlevel = this.subData.level
            //生产放开
            this.applyData.unitAttact = this.applyData.orderId + '|' + this.subData.fileAlias
            // this.applyData.unitAttact = this.applyData.orderId+'|Chrysanthemum.jpg'
            this.$request({
                name: 'addGroup.busiApplySub',
                data: this.applyData,
            }).then(res => {
                if (this.__query("flag") === '1' || this.__query("type") != null) {
                    window.location.href = `http://*************:8090/shop/#/taskMan/JTInfo?unitId=${this.unitId}&custId=${this.custId}&name=${sub.custName}`
                } else {
                    this.Description = '集团编码' + this.unitId + '提交成功|稽核派发成功！'
                    this.showSuccessNext = true
                }
            })
        },
    }
}


