<template>
  <div class="addGroup">
    <van-skeleton :loading="load" :round="false" :row="4" class="pt-5" title/>
    <van-skeleton :loading="load" :round="false" :row="3" class="pt-5" title/>
    <van-skeleton :loading="load" :round="false" :row="3" class="pt-5" title/>
    <van-skeleton :loading="load" :round="false" :row="3" class="pt-5" title/>
    <van-skeleton :loading="load" :round="false" :row="5" class="pt-5" title>
      <van-collapse v-model="activeNames" style="margin-top: -15px">
        <van-collapse-item name="1" title="基本信息" title-class="fw-b">
          <DownMenu v-model="subData.idType" :clickable="!isCheck" :is-required="true" :islable="true"
                    :loading="false"
                    :options="d385110" class="bg-white  py-2"
                    key-value="CODE_VALUE" lable="证件类型" text="CODE_NAME"
                    value-default-code="8" @changeOpv="changeType">
          </DownMenu>
          <div class="bg-white p-3 border-b">
            <van-uploader v-model="fileList" :after-read="afterRead" :before-delete="beforeDelete"
                          :before-read="beforeRead"
                          :deletable="!isCheck" :disabled="isCheck && ocr.isOcr"
                          :max-count="ocr.isCardOcr?3:1"
                          :max-size="10000 * 1024"
                          :multiple="ocr.isCardOcr"
                          @oversize="onOversize">
              <template #preview-cover="{ file }">
                <div class="preview-cover van-ellipsis">{{ file ? file.message : '' }}</div>
              </template>
            </van-uploader>
            <p class="font-small text-right colorRed">{{ ocr.msg }}</p>
          </div>
          <van-field v-model="subData.custName" :disabled="isCheck"
                     error-message-align="right"
                     input-align="right"
                     label="集团名称"
                     minlength="8" placeholder="请输入集团名称" required/>
          <van-field v-model="subData.idIccId" :disabled=" ocr.isCardOcr || idIccIdClickable || isCheck"
                     center
                     error-message-align="right"
                     input-align="right"
                     label="证件号码"
                     placeholder="请输入证件号码" required>
            <template #button>
              <van-button v-if="!ocr.isOcr || ocr.isCardOcr" :disabled="isCheck" size="small" type="primary"
                          @click="checkInfo">验证
              </van-button>
            </template>
          </van-field>
          <van-field v-model="subData.unitLeader" :disabled="(ocr.isCheck && ocr.isOcr)" error-message-align="right"
                     input-align="right"
                     label="法人名称"
                     placeholder="填写法人名称" required/>
          <van-field v-model="subData.idValiddate" :disabled="ocr.isCheck || ocr.isCardOcr" error-message-align="right"
                     input-align="right"
                     label="证件有效期"
                     placeholder="填写证件有效期"
                     readonly
                     required @click="dateShow = !(ocr.isCheck)"
          />
          <van-field v-model="subData.idAddress" error-message-align="right" input-align="right"
                     label="证件地址"
                     placeholder="填写证件地址"
                     required/>
          <van-field v-model="subData.externAddress" error-message-align="right"
                     input-align="right"
                     label="客户实际地址"
                     placeholder="填写客户实际地址"/>
          <DownMenu v-model="subData.level" :clickable="lvClickable" :is-required="true" :itemSelect="lvSelect"
                    :options="codeList.d385836"
                    :value-default-code="subData.level"
                    class="bg-white py-2"
                    key-value="CODE_VALUE" lable="集团等级" text="CODE_NAME"
          >
          </DownMenu>
          <DownMenu v-model="subData.custLevel" :cell-border="false" :is-required="true"
                    :islable="true"
                    :loading="false" :options="codeList.d38587"
                    :value-default-code="subData.custLevel" class="bg-white border-b py-2 border-b py-2"
                    key-value="CODE_VALUE"
                    lable="服务等级" text="CODE_NAME">
          </DownMenu>
          <DownMenu v-model="subData.tradeCode" :clickable="!ocr.isCheck || !isindustry"
                    :is-required="true"
                    :key-value="isindustryInfo.length >0?'BU_INDUSTRY_ID':'CODE_VALUE'"
                    :options="codeList.d38582"
                    :text="isindustryInfo.length >0?'BU_INDUSTRY':'CODE_NAME'" :value-default-code="subData.tradeCode"
                    class="bg-white border-b py-2 border-b py-2"
                    lable="行业类型"
                    @changeOpv="tradeSelect(subData.tradeCode)">
          </DownMenu>


          <DownMenu v-model="subData.industryId" :clickable="!ocr.isCheck || !ocr.isCardOcr || !isindustrysub"
                    :is-required="true"
                    :key-value="isindustryInfo.length >0?'BU_INDUSTRY_SUB_ID':'CODE_VALUE'"
                    :options="d38747"
                    :text="isindustryInfo.length >0?'BU_INDUSTRY_SUB':'CODE_NAME'"
                    :value-default-code="subData.industryId"
                    class="bg-white border-b py-2 border-b py-2"
                    lable="行业类别">
          </DownMenu>
          <DownMenu v-show="labelShow" v-model="subData.label" :is-required="requiredFlag" :options="d385111"
                    :value-default-code="subData.label"
                    class="bg-white border-b py-2"
                    key-value="CODE_VALUE" lable="营销场景" text="CODE_NAME"
          >
          </DownMenu>
          <DownMenu v-show="busicustscenarioShow" v-model="subData.busicustscenario" :is-required="true"
                    :options="codeList.d385834"
                    :value-default-code="subData.busicustscenario"
                    class="bg-white border-b py-2"
                    key-value="CODE_VALUE" lable="场所类别" text="CODE_NAME"
          >
          </DownMenu>
          <DownMenu v-show="twosceneShow" v-model="subData.twoscene" :is-required="true" :item-select="bshow"
                    :options="codeList.d385837"
                    :value-default-code="subData.twoscene"
                    class="bg-white border-b py-2" key-value="CODE_VALUE" lable="细分客群类别" text="CODE_NAME"
          >
          </DownMenu>
          <DownMenu v-model="subData.defscene"
                    :options="codeList.d385838"
                    :value-default-code="subData.defscene"
                    class="bg-white border-b py-2"
                    key-value="CODE_NAME" lable="特色客群(自定义)" text="CODE_NAME"
          >
          </DownMenu>
        </van-collapse-item>
        <van-collapse-item :lazy-render="false" name="2" title="扩展信息" title-class="fw-b">
          <DownMenu v-model="subData.areaType" :cell-border="false" :is-required="true"
                    :islable="true"
                    :loading="false" :options="codeList.d38584"
                    :value-default-code="subData.areaType" class="bg-white border-b py-2 border-b py-2"
                    key-value="CODE_VALUE"
                    lable="经营区域类型" text="CODE_NAME">
          </DownMenu>
          <DownMenu v-model="subData.location" :is-required="true" :islable="true" :loading="false"
                    :options="codeList.d385810" :value-default-code="subData.location"
                    class="bg-white py-2" key-value="CODE_VALUE" lable="所在地市"
                    text="CODE_NAME">
          </DownMenu>
          <DownMenu v-model="subData.busiCompanyId" :cell-border="false" :clickable="!ocr.isCheck" :is-required="true"
                    :islable="true"
                    :loading="false" :options="codeList.d385203"
                    :value-default-code="subData.busiCompanyId" class="bg-white border-b py-2 border-b py-2"
                    key-value="CODE_VALUE"
                    lable="生产经营所在省" text="CODE_NAME" @changeOpv="busiSelect(subData.busiCompanyId,1)">
          </DownMenu>
          <DownMenu v-model="subData.busiLocation" :cell-border="false" :clickable="!ocr.isCheck" :is-required="true"
                    :islable="true"
                    :loading="false" :options="d385203_1"
                    :value-default-code="subData.busiLocation" class="bg-white border-b py-2" key-value="CODE_VALUE"
                    lable="生产经营所在地市" text="CODE_NAME" @changeOpv="busiSelect(subData.busiLocation,2)">
          </DownMenu>
          <DownMenu v-model="subData.busiCountyId" :cell-border="false" :clickable="!ocr.isCheck" :is-required="true"
                    :islable="true"
                    :loading="false" :options="d385203_2"
                    :value-default-code="subData.busiCountyId" class="bg-white border-b py-2" key-value="CODE_VALUE"
                    lable="生产经营所在区县" text="CODE_NAME" @changeOpv="busiSelect(subData.busiCountyId,3)">
          </DownMenu>
          <DownMenu v-model="subData.regManaDepartment" :cell-border="false" :clickable="!ocr.isCheck"
                    :is-required="true"
                    :islable="true"
                    :loading="false" :options="regManaDepartment"
                    :value-default-code="subData.regManaDepartment" class="bg-white border-b py-2"
                    key-value="CODE_VALUE"
                    lable="登记管理部门" text="CODE_NAME">
          </DownMenu>

          <!--<DownMenu :is-required="true" key-value="CODE_VALUE" lable="是否小微企业" class="bg-white border-b py-2"-->
          <!--value-default-code="2"-->
          <!--v-model="subData.micBusiness" text="CODE_NAME" :options="levelOp"-->
          <!--&gt;-->
          <!--</DownMenu>-->
        </van-collapse-item>


        <van-collapse-item name="3" title="联系人信息" title-class="fw-b">
          <van-field
              v-model="subData.linkmanPhone"
              :disabled="isPeople"
              center
              clearable
              label="联系人手机号"
              placeholder="请输入联系人手机号"
              required
          >
            <template #button>
              <van-button :disabled="isPeople" size="small" type="primary" @click="checkPhone">验证</van-button>
            </template>
          </van-field>
          <van-field
              v-model="subData.busiContactPhone"
              :disabled="isContactPeople"
              center
              clearable
              label="商客联系号码"
              placeholder="请输入商客联系号码"
          >
            <template #button>
              <van-button :disabled="isContactPeople" size="small" type="primary" @click="checkContactPhone">验证
              </van-button>
            </template>
          </van-field>
        </van-collapse-item>
        <van-collapse-item name="4" title="选择客户经理" title-class="fw-b">
          <!--                <DownMenu :is-required="true" key-value="CODE_VALUE" lable="集团客户经理"-->
          <!--                          class="bg-white border-b py-2 border-b py-2"-->
          <!--                          :value-default-code="custModMsg.BBOSS_INFO.BUSI_COMPANY_ID" :islable="true"-->
          <!--                          v-model="custModMsg.BBOSS_INFO.BUSI_COMPANY_ID" text="CODE_NAME" :options="codeList.d385203"-->
          <!--                          :cell-border="false" :loading="false" :itemSelect="busiCompanySelect">-->
          <!--            </DownMenu>-->
          <van-field
              v-model="subData.staffLoginName"
              input-align="right"
              is-link
              label="集团客户经理"
              placeholder="选择客户经理"
              readonly
              required
              @click="peopleClick"
          />
        </van-collapse-item>
        <van-collapse-item :lazy-render="false" name="5" title="创建CMIOT客户信息" title-class="fw-b">
          <DownMenu v-model="subData.cmiotSynFlag" :cell-border="false" :clickable="cmiotClickable" :is-required="true"
                    :islable="true"
                    :loading="false" :options="synFlaglist"
                    class="bg-white border-b py-2 border-b py-2" key-value="CODE_VALUE" lable="是否同步CMIOT"
                    text="CODE_NAME" value-default-code="N" @changeOpv="cmiotSelect(subData.cmiotSynFlag)">
          </DownMenu>
            <div v-model="fxpgdShow">
                <p class="title">风险评估单</p>
                <div
                        v-for="(item, index) in fxpgdData"
                        :key="index"
                >
                    <van-checkbox
                            class="radioCtl"
                            shape="square"
                            v-model="item.checked"
                    @change="updateSelectedItems"
                    ></van-checkbox>
                    <div >
                        <van-cell>风险评估单编号：{{ item.RISK_APPLY_ID }}</van-cell>
                        <van-cell>客户名称：{{ item.CUST_NAME }}</van-cell>
                        <van-cell>操作工号：{{ item.LOGIN_NO }}</van-cell>
                        <van-cell>操作时间：{{ item.OP_TIME }}</van-cell>
                        <van-cell>总分：{{ item.totalCount }}</van-cell>
                        <van-cell>风险级别：{{ item.risklevelName }}</van-cell>
                    </div>
                </div>
            </div>
          <DownMenu v-show="importFlagShow" v-model="subData.exportImportFlag" :cell-border="false" :is-required="true"
                    :islable="true"
                    :loading="false" :options="importFlag"
                    class="bg-white border-b py-2 border-b py-2" key-value="CODE_VALUE" lable="是否进出口客户"
                    text="CODE_NAME" value-default-code="">
          </DownMenu>
        </van-collapse-item>
        <van-collapse-item name="5" title="中小微企业鉴定" title-class="fw-b">
          <DownMenu v-model="subData.entType" :is-required="true" :options="codeList.d38583"
                    :value-default-code="subData.entType"
                    class="bg-white border-b py-2"
                    key-value="CODE_VALUE" lable="企业类型" text="CODE_NAME">
          </DownMenu>

          <van-field v-model="subData.annualSales" :formatter="clearNoNum" :show="jdShow" error-message-align="right"
                     input-align="right"
                     label="集团营业额"
                     placeholder="万元"
                     required
                     type="number"
          />
          <van-field v-model="subData.memberCount" :formatter="formatter" error-message-align="right"
                     input-align="right"
                     label="企业员工数"
                     placeholder="填写企业员工数"
                     required
                     type="number"/>
          <DownMenu v-model="subData.smallGrpFlag" :clickable="clickIDTYPE" :is-required="true"
                    :options="codeList.d38510"
                    :value-default-code="subData.smallGrpFlag"
                    class="bg-white border-b py-2" key-value="CODE_VALUE" lable="中小微企业类型"
                    text="CODE_NAME">
          </DownMenu>

          <div class="pt-3 p-2">
            <van-button block plain round type="primary" @click="jd">鉴定</van-button>
          </div>
        </van-collapse-item>
        <van-collapse-item name="4" title="新建集团经纬度" title-class="fw-b">
          <van-field
              v-model="LatLon"
              input-align="right"
              is-link
              label="经纬度信息"
              placeholder="选择经纬度信息"
              readonly
              @click="clickGisMap"
          />
        </van-collapse-item>

      </van-collapse>
      <van-collapse v-model="nextActive" accordion>
        <van-collapse-item v-show="next" name="nextRouteInfo" title="下一环节操作"
                           title-class="fw-b">
          <van-radio-group v-model="routeRadio">
            <van-cell v-for="(route, index) in routeList"
                      :key="index"
                      :title="route | routeTitle"
                      clickable @click="routeRadioCheck(route, index)">
              <template #right-icon>
                <van-radio :name="index"/>
              </template>
            </van-cell>
          </van-radio-group>
        </van-collapse-item>
        <van-collapse-item v-show="next" name="nextLoginInfo" title="选择处理人"
                           title-class="fw-b">
          <van-radio-group v-model="nextLoginNoRadio">
            <van-cell v-for="(nextLoginNo, index) in nextLoginNoList"
                      :key="index"
                      :title="nextLoginNo.LOGIN_NAME + '（' + nextLoginNo.LOGIN_NO + '）'"
                      clickable @click="nextLoginNoRadio = index">
              <template #right-icon>
                <van-radio :name="index"/>
              </template>
            </van-cell>
          </van-radio-group>
        </van-collapse-item>
      </van-collapse>

      <div class="pt-3 p-2">
        <!--            <p class="font-small text-right font-yellow mb-3">上线初期，有问题可加管理员微信35681918处理</p>-->
        <van-button block plain round type="info" @click="subAdd">提交</van-button>
      </div>
    </van-skeleton>
    <van-action-sheet v-model="peopleShow" :style="{ minheight: '35%' }" title="选择客户经理">
      <div class="content">
        <van-search v-model="searchVal" placeholder="请输入客户经理姓名或工号查询" show-action>
          <template #action>
            <div @click="searchInputPeople">搜索</div>
          </template>
        </van-search>
        <van-empty v-if="peopleList.length === 0" description="搜索客户经理" image="search"/>
        <van-list
            v-else
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
        >
          <div style="padding: 0 5px">
            <van-cell
                v-for="item in peopleList"
                :key="item.id"
                :label="item.LOGIN_NO"
                :title="item.LOGIN_NAME"
                is-link
                @click="peopleItem(item)"/>
          </div>
        </van-list>
      </div>
    </van-action-sheet>

    <Success :business-sub-info="unitId" :show="showSuccess" business-name="新建集团"
             business-type="Nomal"></Success>
    <Success :business-sub-info="unitId" :businessName="Description" :show="showSuccessNext"
             businessType="Nomal"></Success>

    <van-dialog v-model="ocrShow" show-cancel-button @confirm="reConfirm">
      <p class="title text-center">识别内容</p>
      <div v-if="!ocr.isCardOcr">
        <van-field v-model="ocrData.CUST_NAME" input-align="center" label="客户名称" label-align="right"/>
        <van-field v-model="ocrData.ID_ICCID" input-align="center" label="证件号码" label-align="right"/>
        <van-field v-model="ocrData.ID_ADDRESS" input-align="center" label="证件地址" label-align="right"/>
        <van-field v-model="ocrData.LEGAL_PERSON_NAME" input-align="center" label="法人名称" label-align="right"/>
      </div>
      <div v-else>
        <van-field v-model="ocrIdData.CUST_NAME" input-align="center" label="客户名称" label-align="right"/>
        <van-field v-model="ocrIdData.ID_ADDRESS" input-align="center" label="证件号码" label-align="right"/>
        <!--<van-field label="证件地址" label-align="right" v-model="ocrIdData.ID_ADDRESS" input-align="center"  />-->
        <van-field v-model="ocrIdData.VALID_DATE_BEGIN + ocrIdData.VALID_DATE_END" input-align="center"
                   label="证件有效期" label-align="right"/>
        <p class="font-small text-right font-yellow mb-3">请检查识别结果是否正确，如不正确，请手动修改</p>

      </div>
    </van-dialog>
    <van-popup v-model="dateShow" position="bottom">
      <van-datetime-picker v-model="currentDate" :min-date="minDate" title="选择年月日" type="date"
                           @confirm="onConfirm"/>
    </van-popup>
    <van-popup ref="mapPopup"
               v-model="showGisMap"
               :lazy-render="false"
               :style="{ height: '80%' }"
               closeable
               position="bottom">
      <div class="content">
        <iframe ref="mapIframe" :height="mapHeight" :src="mapUrl" frameborder="0" width="100%"></iframe>
      </div>
    </van-popup>
  </div>
</template>

<script>
import DownMenu from '@/components/downmenu';
import Success from '@/components/success'
import pageData from "./pagedata";
import {isHaveCN, formatterOnNumber} from '@/utils/validatorUtils'
import axios from 'axios'
import {
  Checkbox, CheckboxGroup, Step, Steps, Cell, CellGroup, Col, Row, Button, Empty, Field, DropdownMenu, DropdownItem,
  Tab, Tabs, Uploader, Calendar, DatetimePicker, NoticeBar, Popup, RadioGroup, Radio, Divider, Toast, Dialog, Collapse
  , CollapseItem, ActionSheet, Search, List, Skeleton, van
} from 'vant';

export default {
  name: "groupAdd",
  mixins: [pageData],
  components: {
    'DownMenu': DownMenu,
    'Success': Success,
    [Checkbox.name]: Checkbox,
    [ActionSheet.name]: ActionSheet,
    [CheckboxGroup.name]: CheckboxGroup,
    [Search.name]: Search,
    [Field.name]: Field,
    [Step.name]: Step,
    [Steps.name]: Steps,
    [Empty.name]: Empty,
    [Button.name]: Button,
    [Col.name]: Col,
    [Row.name]: Row,
    [Cell.name]: Cell,
    [CellGroup.name]: CellGroup,
    [DropdownMenu.name]: DropdownMenu,
    [DropdownItem.name]: DropdownItem,
    [Tab.name]: Tab,
    [Tabs.name]: Tabs,
    [Uploader.name]: Uploader,
    [Calendar.name]: Calendar,
    [DatetimePicker.name]: DatetimePicker,
    [NoticeBar.name]: NoticeBar,
    [Radio.name]: Radio,
    [RadioGroup.name]: RadioGroup,
    [Popup.name]: Popup,
    [Divider.name]: Divider,
    [Dialog.Component.name]: Dialog.Component,
    [Collapse.name]: Collapse,
    [CollapseItem.name]: CollapseItem,
    [List.name]: List,
    [Skeleton.name]: Skeleton
  },
  data() {
    return {
      load: true,
      searchVal: '',
      unitId: '',
      dateShow: false,
      jdShow: true,
      importFlagShow: false,
      labelShow: true,
      custModMsg: {
        CUST_INFO: {},
        GRPCUST_INFO: {},
        CUSTMANAGER_REL: {},
        CUSTOPERATOR_INFO: {},
        GRPCOMPETITION_INFO: {},
        GRPCUSTEXT_INFO: {},
        MICRO_BUSINESS: {},
        BUDGET_INFO: {},
        CUSTVISIT_INFO: {},
        BBOSS_INFO: {}
      },
      ocrShow: false,
      busicustscenarioShow: true,
      twosceneShow: true,
      defsceneShow: false,
      activeNames: ['1', '3', '4'],
      showSuccess: false,
      peopleShow: false,
      loading: false,
      finished: false,
      showGisMap: false,
      LatLon: '',
      total: '',
      multiple: false,
      queryParams: {
        loginNo: '',
        loginName: '',
        custLevel: '',
        pageNum: 1,
        pageAmount: '10',
        flag: '1',
        label: '',
      },
      ocr: {
        isOcr: false,
        isCardOcr: false,
        isCheck: false,
        msg: ''
      },
      lvClickable: false,
      cmiotClickable: false,
      idIccIdClickable: false,
      fileList: [],
      minDate: new Date(),
      currentDate: new Date(),
      arr: [],
      clickLabel: true,
      clickIDTYPE: false,
      mapHeight: '400px',
      jumpImageBase64: '',
      mapUrl: '',
      fxpgkg: 'N',
      requiredFlag: false,
      isindustry: false,
      isindustrysub: false,
      nextLoginNoRadio: '0',
      routeRadio: 0,
      next: false,
      nextActive: '',
      Description: '',
      showSuccessNext: false,
      BEGIN_VALUE: 'D',
      flag: '' // 证件类型标识，用于OCR识别
    };
  },
  filters: {
    routeTitle(value) {
      if (!value) return ''
      return value.ROUTE_NAME
    }
  },
  async created() {
    let p = await this.permission('63851')
    if (!p.data) {
      this.$toast('工号无集团建档权限，请申请权限后办理。')
      return
    }
    this.load = false
    this.dynSqlQry()
    this.basemngDynSqlQryall()
    this.qryLoginBelongCounty()
    this.qryCmiotCustManagerByLoginNo(this.subData.staffLogin)
    this.qryLoginMsg()
    this.qryLoginGroupLevel()  //查询工号归属
    this.getSeqByNM({   //查询BUSI_ID
      seqName: 'commonSeq'
    })
    this.getorderId({   //查询BUSI_ID
      seqName: 'orderIdSeq'
    })
      this.qryGrpCustUnitId()  //获取UNIT_ID

    //判断是不是链接携带参数，延迟是因为变更证件类型会清空图片列表,其实直接设置个url就行（应该大概，没试）
    setTimeout(() => {
      if (this.$route.query.type) {
        this.subData.idType = this.$route.query.type
        if (this.$route.query.phoneNo) {
          this.subData.linkmanPhone = this.$route.query.phoneNo
        }
        if (this.$route.query.image) {
          this.imageUrlToBase64(this.$route.query.image, (base64) => {
            let item = {
              content: base64,
              comContent: base64,
              file: {
                name: 'group.jpeg',
                message: '营业执照'
              },
              message: '营业执照'
            }
            if (this.subData.idType == 17) {
              this.jumpImageBase64 = base64
              if (this.$route.query.name) {
                this.subData.custName = this.$route.query.phoneNo
              }
            } else {
              this.jumpImageBase64 = ''
              this.fileList.push(item)
              this.afterRead(item)
            }
          })
        }
      }
    }, 3000)

    this.mapHeight = document.documentElement.clientHeight * 0.8
    window.addEventListener("message", e => {
      switch (e.data.operator) {
        case "address":
          console.log(e.data.data)
          this.subData.lat = e.data.data.lat
          this.subData.lon = e.data.data.lon
          this.showGisMap = false
          this.LatLon = "经度:" + e.data.data.lon + "\n纬度:" + e.data.data.lat
          break;
      }
    })
  },
  mounted() {
    if (typeof HostApp === "object") {
      HostApp.location(res => {
        this.$refs.mapIframe.onload = () => {
          setTimeout(() => {
            this.$refs.mapIframe.contentWindow.postMessage({
              "scene": "map",
              "operator": "access",
              "timestamp": Date.now(),
              "data": {}
            }, "*");
          }, 2000)
        };
        this.mapUrl = "/record-map-point/#/?coords=" + res.lon + "," + res.lat
      })
    }
  },
  methods: {


      // 更新选中项
      updateSelectedItems() {

          // 清空原数组
          this.subData.riskApplyInfo = [];
          // 遍历添加选中的项目
          console.log(this.fxpgdData)

          this.fxpgdData.forEach(item => {
              if (item.CUST_NAME != this.subData.custName) {
                  this.$toast("风险评估单中的客户名称与当前客户的客户名称必须一致，不一致则无法关联")
                  this.subData.riskApplyId = ''
                  return
              }else{
                  this.subData.riskApplyId = 'Y'
              }
              if (item.checked) {
                  this.subData.riskApplyInfo.push({
                    RISK_APPLY_ID: item.RISK_APPLY_ID,
                      scenarioElement: item.scenarioElement_desc,
                      scenarioElement_two: item.scenarioElement_two_desc,
                      totalCount: item.totalCount,
                      risklevelName: item.risklevelName,
                  });
              }
          });

          console.log("当前选中项：", this.subData.riskApplyInfo);

      },
    //图片地址转换base64
    imageUrlToBase64(imageUrl, back) {
      //一定要设置为let，不然图片不显示
      let image = new Image();
      image.crossOrigin = '';
      image.crossOrigin = "Anonymous"; //注意存放顺序
      //解决跨域问题
      image.setAttribute('crossOrigin', 'anonymous');
      image.src = imageUrl
      let that = this;
      //image.onload为异步加载
      image.onload = () => {
        var canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        var context = canvas.getContext('2d');
        context.drawImage(image, 0, 0, image.width, image.height);
        var quality = 0.8;
        //这里的dataurl就是base64类型
        var dataURL = canvas.toDataURL("image/jpeg", quality);//使用toDataUrl将图片转换成jpeg的格式,不要把图片压缩成png，因为压缩成png后base64的字符串可能比不转换前的长！
        back(dataURL)
      }
    },
    getBase64(url, back) {
      axios
          .get(url, {
            responseType: 'arraybuffer',
            headers: {"Access-Control-Allow-Origin": "*"}
          })
          .then(response => back('data:image/jpeg;base64,' + Buffer.from(response.data, 'binary').toString('base64')))
    },
    clickGisMap() {
      this.showGisMap = true
    },
    formatter(value) {
      // 过滤输入的数字
      return formatterOnNumber(value);
    },
    clearNoNum(value) {
      value = value.replace(/[^\d.]/g, "");  //清除"数字"和"."以外的字符
      value = value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的
      value = value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
      value = value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数
      if (value.indexOf(".") < 0 && value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
        value = parseFloat(value);
      }
      return value
    },
    // 时间
    formatDate(date) {
      let y = date.getFullYear();
      let m = date.getMonth() + 1;
      m = m < 10 ? '0' + m : m;
      let d = date.getDate();
      d = d < 10 ? ('0' + d) : d;
      return y.toString() + m.toString() + d.toString();
    },
    //点击有效期选择
    onConfirm(date) {
      this.subData.idValiddate = this.formatDate(date)
      this.dateShow = false;
    },
    //集团等级选择
    lvSelect(a, i) {
      console.log(a.BEGIN_VALUE)
      console.log(i)
      this.BEGIN_VALUE = a.BEGIN_VALUE
      this.queryParams.flag = '1'
      this.queryParams.pageNum = 1
      this.queryParams.loginName = ''
      this.queryParams.loginNo = ''
      // this.queryParams.label = this.subData.label
      this.queryParams.custLevel = this.subData.level
      if (a.BEGIN_VALUE != 'C' && a.BEGIN_VALUE != 'D') {
        this.next = true  //AB类集团展示下一环节审批人
        this.getStartRouteInfosByProdId(this.applyData.loginGroup)  //查询下一环节
        this.requiredFlag = false
        // this.subData.label = '07'
        // this.d385111 = this.arr.filter(i => i.CODE_VALUE == '07')//行业市场
        this.clickLabel = false
        // this.labelShow = false
      } else {
        this.next = false
        this.requiredFlag = true
        // this.d385111 = this.arr.filter(i => i.CODE_VALUE != '07')//行业市场
        this.subData.label = this.d385111[0].CODE_VALUE
        this.clickLabel = true

        // this.labelShow = true
      }
      // if (!this.subData.label) {
      //     this.$toast('请选择营销场景')
      //     return
      // }
      this.qryManagerLoginInRegion(this.queryParams)
    },
    //中小微企业鉴定服务
    jd() {
      this.qryIdentificEnterprise()
    },
    //提交
    subAdd() {

      if (!this.subData.idValiddate) {
        this.$toast('请选择证件有效期')
        return
      }
      if (!this.subData.custName) {
        this.$toast('请输入集团名称')
        return
      }
      if (!this.subData.idIccId) {
        this.$toast('请输入集团证件号码')
        return
      }
      if (!this.subData.idAddress) {
        this.$toast('请输入集团证件地址')
        return
      }

      if (!this.subData.unitLeader) {
        this.$toast('请输入法人名称')

        return
      }
      if (!this.subData.areaType) {
        this.$toast('请选择经营区域类型')
        return
      }
      if (!this.subData.tradeCode) {
        this.$toast('请选择行业类型')
        return
      }
      if (!this.subData.industryId) {
        this.$toast('请选择行业类别')
        return
      }
      if (!this.subData.level) {
        this.$toast('请选择集团等级')
        return
      }
      if (!this.subData.regManaDepartment) {
        this.$toast('登记管理部门')
        return
      }
      if (!this.subData.busiCompanyId) {
        this.$toast('请选择生产经营所在省')
        return
      }
      if (!this.subData.busiLocation) {
        this.$toast('请选择生产经营所在地市')
        return
      }
      if (!this.subData.busiCountyId) {
        this.$toast('请选择生产经营所在区县')
        return
      }
      if (!this.subData.busiCountyId) {
        this.$toast('请输入注册资本')
        return
      }
      if (!this.isCheck) {
        this.$toast('请先检验集团信息')
        return
      }
      if (!this.isPeople) {
        this.$toast('请先检验联系人信息')
        return
      }
      if (this.subData.idType == '17' && !this.subData.busiContactPhone) {
        this.$toast('请输入商客联系号码')
        return
      }
      if (this.subData.busiContactPhone && !this.isContactPeople) {
        this.$toast('请先检验商客联系号码')
        return
      }
      if (!this.subData.fileBase64 && !this.subData.fileAlias) {
        this.$toast('请上传照片信息')
        return
      }
      if (!this.subData.label) {
        this.$toast('请选择营销场景标签')
        return
      }
      if (!this.subData.fileBase64 && this.subData.fileName === '') {
        this.$toast('附件名称不能为空')
        return
      }
      if (!this.subData.smallGrpFlag) {
        this.$toast('请鉴定中小微企业类型')
        return
      }

      if (this.subData.idType != '11' && this.subData.idType != '17' && this.subData.cmiotSynFlag == 'Y' && this.fxpgkg == 'Y' && this.subData.riskApplyId == '') {
        this.$toast('请关联风险评估单')
        return
      }
      if (this.subData.twoscene == '05' && !this.subData.defscene) {
        this.$toast("特色客群不能为空")
        return
      }
      this.approveGrpCustInfo(this.subData)

    },
    //行业类型级联查询
    tradeSelect(value) {
      this.dynSqlQryTrade(value)
    },
    //级联查询
    busiSelect(value, type) {
      if (value) {
        if (type !== 3) {
          this.dynSqlQryCounty(value, type)
        } else {
          this.subData.busiCountyName = this.d385203_2.find(i => {
            return i.CODE_VALUE == value
          }).CODE_NAME
        }
      }
    },

    //级联查询
    cmiotSelect(value) {
      if (value == 'Y') {
        this.importFlagShow = true
        if (this.subData.idType != '11' && this.subData.idType != '17') {
          this.qryfxpgkg()
        }

      } else {
        this.importFlagShow = false
        this.subData.exportImportFlag = ''
      }
    },
    //选择人员
    peopleItem(item) {

      this.peopleShow = false
      this.subData.staffLoginName = item.LOGIN_NAME
      this.subData.staffLogin = item.LOGIN_NO
      this.qryCmiotCustManagerByLoginNo(item.LOGIN_NO)

    },
    //验真点击时间，不是ocr的情况调用验真接口，不验真的调用重复校验
    checkInfo() {
      this.industryQuery(this.subData.custName, this.subData.idIccId, this.subData.idType)
    },
    //人员选择搜索
    searchInputPeople(value) {
      this.loading = true;
      this.queryParams.pageNum = 1
      this.peopleList = []
      this.queryParams.custLevel = this.subData.level
      this.finished = false
      if (isHaveCN(this.searchVal)) {
        this.queryParams.loginNo = ''
        this.queryParams.loginName = this.searchVal
      } else {
        this.queryParams.loginName = ''
        this.queryParams.loginNo = this.searchVal
      }
      this.queryParams.label = this.subData.label
      console.log(this.queryParams)
      this.qryManagerLoginInRegion(this.queryParams)
    },
    //选择客户经理
    peopleClick() {
      if (this.subData.level) {
        this.queryParams.custLevel = this.subData.level
        // this.queryParams.label = this.subData.label
        this.queryParams.flag = '0'
        this.loading = true;
        this.queryParams.pageNum = 1
        this.queryParams.loginName = ''
        this.queryParams.loginNo = ''
        this.peopleList = []
        this.finished = false
        this.qryManagerLoginInRegion(this.queryParams)
      } else {
        this.$toast('请先选择集团等级')
      }
    },

    //检验联系人手机号码
    checkPhone() {

      if (this.subData.linkmanPhone && this.subData.linkmanPhone.length === 11) {
        this.contactPhoneNo(this.subData.linkmanPhone)
      } else {
        this.$toast('请输入正确手机号码')
      }
    },
    //检验商客联系号码
    checkContactPhone() {

      if (this.subData.busiContactPhone && this.subData.busiContactPhone.length === 11) {
        this.contactPhoneNo2(this.subData.busiContactPhone)
      } else {
        this.$toast('请输入正确手机号码')
      }
    },
    // 证件类型变更
    changeType(item) {
      this.flag = item
      console.log('证件类型变更')
      console.log(item)
      let i = this.d385110.find(res => {
        return res.CODE_VALUE === item
      })
      if (this.subData.idTypeName !== i.CODE_NAME) {
        this.fileList = []
        this.subData.fileBase64 = ''
      }
      this.subData.idTypeName = i.CODE_NAME
      if (item == '17') {
        this.subData.level = 'D'
        this.requiredFlag = true
        this.lvClickable = false
        this.subData.productType = '01' //集团子类型
        this.subData.productTypeName = '个体工商户' //集团子类型名称
        this.subData.smallGrpFlag = '0'  //默认其他
      } else {
        this.lvClickable = true
        this.subData.productType = '00' //集团子类型
        this.subData.productTypeName = '法人' //集团子类型名称

      }
      //单位介绍信/身份证门头照需自动生成证件id，且不允许修改
      if (item === '11' || item === '17') {
        this.idIccIdClickable = true
        this.createGrpCustIccId(this.subData.idType)
      } else {
        this.subData.idIccId = ''
        this.idIccIdClickable = false
      }
      if (i.ORC_FLAG === 'Y') {
        this.ocr.msg = '请上传' + this.subData.idTypeName
        this.ocr.isOcr = true
        if (item == '17') {
          this.jdShow = false
          this.subData.micBusiness = '1'
          this.subData.level = 'D'
          this.requiredFlag = true
          this.ocr.isCardOcr = true
          this.ocr.msg = '请上传身份证正面，反面，门头照'
          this.lvSelect('', '')
        } else {
          this.jdShow = true
          this.subData.micBusiness = '0'
          this.ocr.isCardOcr = false
        }
      } else {
        this.ocr.msg = '请上传证件照片'
        this.ocr.isOcr = false
        this.ocr.isCardOcr = false
      }
      if (i.VERIFIC_FLAG === 'Y') {
        this.ocr.isCheck = true
        this.isindustry = true

      } else {
        this.ocr.isCheck = false
        this.isindustry = false
      }
      // let ocrTypes = ['8','20','9','10']
      // if (ocrTypes.indexOf(item)!=-1){
      //     this.ocr.msg = '请上传' + this.subData.idTypeName
      //     this.ocr.isOcr = true
      // }else {
      //     if (item == '17'){
      //         this.ocr.isCardOcr = true
      //         this.ocr.msg = '请上传身份证正面，反面，门头照'
      //     }else {
      //         this.ocr.isCardOcr = false
      //     }
      //     this.ocr.isOcr = false
      // }
      // let checkTypes = ['11','18','19','17']
      // if (checkTypes.indexOf(item) == -1){
      //     this.ocr.isCheck = true
      // }else {
      //     this.ocr.isCheck = false
      // }
      console.log(item, this.ocr.isCheck, i.VERIFIC_FLAG)
    },
    //选择文件之前调用
    beforeRead(file) {
      if (this.ocr.isCardOcr) {
        if (this.fileList.length === 0) {
          file.message = '身份证正面'
        }
        if (this.fileList.length === 1) {
          file.message = '身份证反面'
        }
        if (this.fileList.length === 2) {
          file.message = '门头照'

        }
      } else {
        file.message = this.subData.idTypeName
      }
      return true;
    },
    beforeDelete() {
      if (this.ocr.isCardOcr && this.subData.idType == '17') {
        this.fileList = []
      } else {
        return true
      }
    },
    //选择完成后回调，身份证门头照上传三张图片，身份证正反面与门头照，其余需要ocr类型的直接调用ocr识别接口
    afterRead(file) {

      console.log(file)
      file.status = 'done';
      file.message = '身份证正面';
      var image = new Image()
      image.src = file.content // 设置image的地址为base64的地址
      image.onload = () => { // 图片加载完成后才能进行压缩处理，从而转换为base64 进行赋值
        let width = image.width // 图片宽度
        let height = image.height
        if (file.file.size > 2097152) {
          file.comContent = this.compress(image, width, height, 0.8)
        } else {
          file.comContent = file.content
        }
        if (this.ocr.isCardOcr) {
          if (this.fileList.length === 1) {
            this.fileList[0].message = '身份证正面'
          }
          //链接跳转门头照类型判断
          if (this.fileList.length === 2) {
            file.message = '身份证反面'
            if (this.jumpImageBase64) {
              let item = {
                content: this.jumpImageBase64,
                comContent: this.jumpImageBase64,
                file: {
                  name: 'mentou.jpeg',
                  message: '门头照'
                },
                message: '门头照'
              }
              this.fileList.push(item)
              this.afterRead(item)
              this.jumpImageBase64 = ''
            }
          }
          if (this.fileList.length === 3) {
            file.message = '门头照'
            let name = file.file.name;
            this.subData.fileName = Date.now() + '.' + name.substring(name.lastIndexOf(".") + 1, name.length);
            this.identificLicensePerson(this.fileList[0].comContent.split(',')[1],
                this.fileList[1].comContent.split(',')[1], this.fileList[2].comContent.split(',')[1])
          }
        } else {
          file.message = this.subData.idTypeName
          let name = file.file.name;
          this.subData.fileName = Date.now() + '.' + name.substring(name.lastIndexOf(".") + 1, name.length);
          // this.subData.fileName = file.file.name

          if (this.ocr.isCheck) {
            this.queryOcr(file.comContent.split(',')[1], this.subData.idType)
            // this.callOcrApi(file.comContent.split(',')[1]);
            this.callOcrApi({
              image: file.comContent.split(',')[1],
              flag: this.flag,
            });
          } else {
            this.subData.fileBase64 = file.comContent.split(',')[1]
            // 调用OCR接口进行识别
            this.callOcrApi({
              image: file.comContent.split(',')[1],
              flag: this.flag,
            });
          }
        }
      }
      // this.uploadFile(file.content,file.file.name,this.fileType)
    },
    /**
     * 读取完成确认按钮，通过证件类型判断
     */
    reConfirm() {
      if (this.ocr.isCheck) {
        this.grpChkAndEcVerific(this.ocrData.CUST_NAME, this.ocrData.ID_ICCID, this.subData.idType,
            this.ocrData.LEGAL_PERSON_NAME)
      } else {
        this.subData.unitLeader = this.ocrIdData.CUST_NAME
        // this.subData.idIccId = this.ocrIdData.ID_ICCID
        this.subData.idAddress = this.ocrIdData.ID_ADDRESS //集团证件地址groupChange.dynSqlQry
        if (this.subData.idAddress.length < 6) {
          Toast('证件地址');
        }
        this.subData.externAddress = this.ocrIdData.ID_ADDRESS
        this.subData.idValiddate = this.ocrIdData.VALID_DATE_END == '长期' ? '20501231' : this.ocrIdData.VALID_DATE_END //集团证件有效期
      }
    },
    routeRadioCheck(route, index) {
      this.routeRadio = index
      if (route.ROLE_ID !== "-1") {
        this.getGroupLoginInfos(route.ROLE_ID)
      }

    },

    onLoad() {
      this.queryParams.pageNum++
      this.qryManagerLoginInRegion(this.queryParams)
    },
    //文件大小提示
    onOversize(file) {
      console.log(file);
      Toast('文件大小不能超过 10M');
    },
    /*
     * 图片压缩
     * img   原始图片
     * width  压缩后的宽度
     * height  压缩后的高度
     * ratio  压缩比率
    */
    compress(img, width, height, ratio) {
      let canvas, ctx, img64;
      canvas = document.createElement('canvas')
      canvas.width = width;
      canvas.height = height;
      ctx = canvas.getContext("2d");
      ctx.drawImage(img, 0, 0, width, height);
      img64 = canvas.toDataURL("image/jpeg", ratio);
      return img64;
    },
    bshow() {
      if (this.subData.twoscene == '05') {
        this.defsceneShow = true

      } else {
        this.defsceneShow = false

      }

    },
    // callOcrApi(base64Img) {
    //   const requestData = {
    //     base64_img: base64Img,
    //     model_name_list: ["ocr"],
    //     table_coord: {
    //       "集团名称": [127, 251, 432, 297],
    //       "信用代码": [614, 190, 809, 229]
    //     }
    //   };
    //
    //   axios.post('http://think.igalaxycn.com:4002/run_standalone', requestData)
    //       .then(response => {
    //         console.log(response.data.status)
    //         if (response.data.status === 200) {
    //           console.log(1)
    //           const ocrData = response.data.data[0].result[0];
    //
    //           if (ocrData["集团名称"]) {
    //             this.subData.custName = ocrData["集团名称"];
    //           }
    //           if (ocrData["信用代码"]) {
    //             this.subData.idIccId = ocrData["信用代码"];
    //           }
    //         } else {
    //           this.$toast('OCR识别失败，请手动输入');
    //         }
    //       })
    //       .catch(error => {
    //         console.error('OCR API Error:', error);
    //         this.$toast('OCR识别服务异常，请手动输入');
    //       });
    // },
  }
}
</script>

<style lang="scss" scoped>
@import './style';
</style>
