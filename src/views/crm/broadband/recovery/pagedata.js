let debug = {}
if (process.env.NODE_ENV === 'development') {
    debug = require('./debugData.js');
}

export default {
    data() {
        return {
            isDebug: false,
            depositList: [],
            operationList: [],
            convg: {
                IS_MOBILE_CONVG: 'N'
            }
        }
    },
    methods: {
        /**
         * 查询押金详情
         * @param idIccid 证件号码
         * @param idType 证件类型
         * @param phoneNo 服务号码
         */
        depositQuery({idIccid, idType, serviceNo}) {
            this.$request({
                isDebug: this.isDebug,
                debugData: debug.depositQuery,
                name: 'recovery.deposit.query',
                data: {idIccid, idType, serviceNo}
            }).then(res => {
                this.depositList = res.data.filter(item => !!item.RESOURCE_NO)
            });
        },
        // 查询操作类型
        operationQuery(data) {
            this.$request({
                isDebug: this.isDebug,
                debugData: debug.operationQuery,
                name: 'recovery.operation.query',
                data: data
            }).then(res => {
                this.operationList = res.data.map(item => {
                    return {
                        name: item.ID_DESC,
                        value: item.VALUE_ID
                    }
                })
                this.operationAction = true
            });
        },
        // 查询缴费信息
        chkMobileConvg(phoneNo, callback) {
            this.$request({
                isDebug: this.isDebug,
                debugData: debug.chkMobileConvgData,
                name: 'recovery.device.chkMobileConvg',
                data: {phoneNo}
            }).then(res => {
                this.convg = res.data
                if (callback && typeof callback() === 'function') {
                    callback()
                }
            });
        },
        async qryCustInfoByCond(idIccid) {
            let result
            await this.$request({
                isDebug: this.isDebug,
                debugData: debug.custIdList,
                name: 'recovery.device.qryCustInfoByCond',
                data: {idIccid}
            }).then(res => {
                result = res.data.indexOf(this.currentItem.CUST_ID + '') > -1
            });
            return result
        },
        // 提交
        depositSubmit(data) {
            this.$request({
                isDebug: this.isDebug,
                debugData: debug.queryMasterData,
                name: 'qqw/queryMaster',
                data: {phoneNo: data.serviceNo}
            }).then(masterData => {
                let serviceType;
                let businsessType;
                if (masterData.MASTER_SERV_ID === '1002') {
                    if (masterData.BRAND_ID === 'kd') {
                        serviceType = '01'
                    } else {
                        serviceType = '99'
                    }
                    businsessType = '0'
                } else if (masterData.MASTER_SERV_ID === '3585') {
                    serviceType = '01'  //iptv
                    businsessType = '1'
                } else if (masterData.MASTER_SERV_ID === '3587') {
                    serviceType = '99' //爱家TV
                    businsessType = '1'
                } else if (masterData.MASTER_SERV_ID === '3713') {
                    serviceType = '01'
                    businsessType = '2'
                } else if (masterData.MASTER_SERV_ID === '3728') {
                    serviceType = '99'
                    businsessType = '2'
                } else {
                    this.$request
                    ({
                        isDebug: this.isDebug,
                        debugData: debug.deviceData,
                        name: 'recovery.queryFttrBroadband',
                        data: {IdNo: this.csdata.ID_NO}
                    }).then(res => {
                        if (res.data.ont) {
                            serviceType = '01'
                            businsessType = '4'
                        } else {
                            return this.$toast.success('暂不支持该业务类型查询串码')
                        }
                    });
                }

                // 查询设备串号
                this.$request({
                    isDebug: this.isDebug,
                    debugData: debug.deviceData,
                    name: 'recovery.device.query',
                    data: {
                        account: data.serviceNo,
                        serviceType: serviceType,
                        businsessType: businsessType,
                        accessTime: this.csdata.OPEN_TIME
                    }
                }).then(res => {
                    if (res.data.ont) {
                        this.sn = res.data.data.ont.sn
                    }
                    this.$request({
                        isDebug: true,
                        debugData: debug.operationQuery,
                        name: 'recovery.deposit.submit',
                        data: {
                            resourceNo: this.sn,
                            ...data
                        }
                    }).then(() => {
                        this.successShow = true
                    });
                });

                // 测试
                // this.$request({
                //     isDebug: this.isDebug,
                //     debugData: debug.operationQuery,
                //     name: 'recovery.deposit.submit',
                //     data: {
                //         resourceNo: data.resNo,
                //         ...data
                //     }
                // }).then(() => {
                //     this.successShow = true
                // });
            });
        }
    }
}
