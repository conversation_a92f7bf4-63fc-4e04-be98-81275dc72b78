var moaBridge = {};
var ua = navigator.userAgent.toLowerCase();
var openDownloadpath = '';
var cbFuncName = "";
var qrCodeCallBack;

/**
 * 与下载一致，后续改进成在线预览。
 */
moaBridge.preview = function(param) {

    openDownloadpath = param.fileName;
    var downloadJson = '';

    if (sessionStorage.getItem("sysId") != undefined
        && sessionStorage.getItem("sysId") != null
        && sessionStorage.getItem("sysId") == 'scm') {
        var url = param.url.replace(
            param.url.split("/")[0] + "/" + param.url.split("/")[1] + "/"
            + param.url.split("/")[2] + "/",
            window.location.protocol + "//" + window.location.host + "/");

        downloadJson += '{"fileURL":"' + url + '","savePath":"'
            + param.fileName
            + '","backId":"download","backFunc":"backFunc1"}';
    } else {
        downloadJson += '{"fileURL":"' + param.url + '","savePath":"'
            + param.fileName
            + '","backId":"download","backFunc":"backFunc1"}';
    }
    if (/hefeixin/.test(ua)) { // hefeixin负一屏
        if (/iphone|ipad|ipod/.test(ua)) {
            navigator.WebContainer.downloadFile(downloadJson);
        } else if (/android/.test(ua)) {
            window.WebContainer.downloadFile(downloadJson);
        }
    } else if (/uniontec/.test(ua)) {
        if (/iphone|ipad|ipod/.test(ua) || /android/.test(ua)) {
            if (typeof window.WebContainer.showLoading == 'function') {
                window.WebContainer.showLoading();
            }
            window.WebContainer.downloadFile(downloadJson);
        }
    } else {

        var a = document.createElement('a');
        a.setAttribute('download', param.fileName);
        a.href = param.url;
        // 兼容firefox
        $('body').append(a);
        a.click();
        $(a).remove();
    }
}

moaBridge.download = function(param) {
    openDownloadpath = param.fileName;
    var downloadJson = '';

    if (sessionStorage.getItem("sysId") != undefined
        && sessionStorage.getItem("sysId") != null
        && sessionStorage.getItem("sysId") == 'scm') {
        var url = param.url.replace(
            param.url.split("/")[0] + "/" + param.url.split("/")[1] + "/"
            + param.url.split("/")[2] + "/",
            window.location.protocol + "//" + window.location.host + "/");

        downloadJson += '{"fileURL":"' + url + '","savePath":"'
            + param.fileName
            + '","backId":"download","backFunc":"backFunc1"}';
    } else {
        downloadJson += '{"fileURL":"' + param.url + '","savePath":"'
            + param.fileName
            + '","backId":"download","backFunc":"backFunc1"}';
    }
    if (/hefeixin/.test(ua)) { // hefeixin负一屏
        if (/iphone|ipad|ipod/.test(ua)) {
            navigator.WebContainer.downloadFile(downloadJson);
        } else if (/android/.test(ua)) {
            window.WebContainer.downloadFile(downloadJson);
        }
    } else if (/uniontec/.test(ua)) {
        if (/iphone|ipad|ipod/.test(ua) || /android/.test(ua)) {
            if (typeof window.WebContainer.showLoading == 'function') {
                window.WebContainer.showLoading();
            }
            window.WebContainer.downloadFile(downloadJson);
        }
    } else {

        var a = document.createElement('a');
        a.setAttribute('download', param.fileName);
        a.href = param.url;
        // 兼容firefox
        $('body').append(a);
        a.click();
        $(a).remove();
    }

}

function GetQueryString(url, name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    // var r = window.location.search.substr(1).match(reg);
    var r = url.substr(1).match(reg);
    if (r != null)
        return unescape(r[2]);
    return null;
}

// moaBridge.goBack = function(i, reload) {
// if (window.history.length > 2) {
// window.history.go(i);
// } else {
// window.WebContainer.goToback();
// }
// if (reload) {
// location.reload();
// }
// };

moaBridge.close = function() {// 资金系统返回调用的close方法
    // mui.alert('返回');
    if (sessionStorage.getItem("sysId") != undefined
        && sessionStorage.getItem("sysId") != null
        && sessionStorage.getItem("sysId") == 'scm') {
        window.location.href = sessionStorage.getItem("scmBackList");
    } else if (sessionStorage.getItem("sysId") != undefined
        && sessionStorage.getItem("sysId") != null
        && sessionStorage.getItem("sysId") == 'DFMS') {
        window.location.href = sessionStorage.getItem("DfmsBackList");
    } else {
        window.WebContainer.goToback();
    }
}

moaBridge.backToHome = function() {
    moaBridge.close();
}
moaBridge.closeDoc = function() {
    moaBridge.close();
}
function backFunc1(jsonString) {

    if (JSON.parse(jsonString).status == '1') {
        var openDownloadJson = '{"filePath":"' + openDownloadpath
            + '","backId":"open","backFunc":"backFunc2"}';
        // 获取手机类型
        var ua = navigator.userAgent.toLowerCase();
        // 判断手机型号，采取不同的调用方式
        if (/iphone|ipad|ipod/.test(ua) || /android/.test(ua)) {
            if (/uniontec/.test(ua)
                && typeof window.WebContainer.dissLoading == 'function') {
                window.WebContainer.dissLoading();
            }
            window.WebContainer.openFile(openDownloadJson);
        } else {
            alert("该功能只支持ios和android系统！");
        }
    } else {
        alert("文件下载失败！");
    }

}
// 一线客服打开新的webView
// moaBridge.openDoc({
// url:"",//目标地址
// title:"",//导航栏名称，客户端使用
// mode:""//full表示H5全屏，否则客户端会渲染导航栏
// });
moaBridge.openDoc = function(param) {
    console.log("ua:" + ua);
    console.log(param);
    var openDocStr = '{"url":"' + param.url + '","title":"' + param.title
        + '","mode":"' + param.mode + '"}';
    window.WebContainer.openDoc(openDocStr);
}

moaBridge.getQRCode = function(param) {
    if (/uniontec/.test(ua)&& typeof window.WebContainer.startLocation == 'function') {
        var QRCodeStr = '{"abID":"' + param.abID + '","cbFuncName":"'+ param.cbFuncName + '"}';
        window.WebContainer.getQRCode(QRCodeStr);
    }else{
        alert("请使用山西移动OA客户端。");
    }
}

/**
 * action:0//代表传入的拍照图片,1代表录像 camera:1//代表前置摄像头，2代表后置
 *
 */
moaBridge.cameraUpdate = function(action, camera) {

    if (/uniontec/.test(ua)
        && typeof window.WebContainer.cameraUpdate == 'function') {
        var cameraUpdateStr = '{"action":"' + action + '","camera":"' + camera
            + '","backFunc":"cameraUpdateBack"}';
        // mui.alert(cameraUpdateStr);
        window.WebContainer.cameraUpdate(cameraUpdateStr);
    } else {
        alert("请使用山西移动OA客户端。");
    }
}

function cameraUpdateBack(params) {
    // alert(params);
}

moaBridge.upload = function(param) {
    var cameraload = '{"abID":"' + param.abID + '","cbFuncName":"'
        + param.cbFuncName + '","size":"' + param.size + '","fileType":"'
        + param.fileType + '"}';
    window.WebContainer.upload(cameraload);
}
// abID:"abilityName",
// cbFuncName:"callbackFunctionName",
// size: "8M",
// fileType: ["jpg", "jpeg", "png"]
moaBridge.accessPhoto = function(param) {
    var cameraload = '{"abID":"' + param.abID + '","cbFuncName":"'
        + param.cbFuncName + '","size":"' + param.size + '","fileType":"'
        + param.fileType + '"}';
    window.WebContainer.accessPhoto(cameraload);
}

var loctionInfo = "";
var loctionState = "";
var loctionMsg = "";
moaBridge.getLoction = function(callback) {
    // alert("定位开始");
    if (/uniontec/.test(ua)
        && typeof window.WebContainer.startLocation == 'function') {
        /*
         * if (/iphone|ipad|ipod/.test(ua)) { var locationResult =
         * '{"code":"error","msg":"获取位置信息失败，ios终端暂不支持该功能。","date":"' +
         * loctionInfo + '"}'; alert(locationResult); callback(locationResult); }
         */

        // 调用原生方法
        window.WebContainer.startLocation('{"backFunc":"locationBak"}');
        // 定时任务检查结果
        let startTime = new Date(); // 开始时间
        var times = setInterval(function() {
            var locationResult = '';
            if (loctionState != "") {
                clearInterval(times);
                if (loctionState == '1') {// 成功
                    locationResult = '{"code":"success","msg":"' + loctionMsg
                        + '","date":' + loctionInfo + '}';
                } else {// 失败
                    locationResult = '{"code":"error","msg":"' + loctionMsg
                        + '","date":' + loctionInfo + '}';
                }
                // alert(locationResult);
                // 回调结果给业务侧
                callback(locationResult);

            }
            let endTime = new Date(); // 结束时间
            let usedTime = endTime - startTime; // 相差的毫秒数
            if (usedTime > 30000) {
                clearInterval(times);
                locationResult = '{"code":"error","msg":"获取位置信息超时","date":"'
                    + loctionInfo + '"}';
                // alert(locationResult);
                callback(locationResult);
            }
        }, 1000);
    } else {
        var locationResult = '{"code":"error","msg":"获取位置信息失败，请在移动OA使用该功能。","date":"'
            + loctionInfo + '"}';
        // alert(locationResult);
        callback(locationResult);
    }
}

// 获取位置信息回调方法
function locationBak(jsonString) {
    var data = JSON.parse(jsonString);
    loctionState = data.status;// 获取位置信息状态
    loctionMsg = data.msg;// 获取位置信息提示信息
    if (data.status == '1') {// 定位成功位置信息
        loctionInfo = JSON.stringify(data.params);
    }

}


moaBridge.uploadFileWexin = function(params) {

    if (/uniontec/.test(ua)) {
        if (/iphone|ipad|ipod/.test(ua)) {
            alert("苹果终端不支持该功能!");
        } else {
            if (typeof window.WebContainer.fileupload == 'function') {
                window.WebContainer.fileupload(params);
            } else {
                alert("当前版本不支持该功能，请升级后使用！");
            }
        }
    } else {
        alert("当前终端不支持该功能!");
    }
}

function badgeCallBack(num){
    console.log("智信角标回调方法============================>"+num)


    sessionStorage.setItem("zxBadgeNum",num);

    setBadgeNum("badgezxim");
    setBadgeNum("sild_badgezxim");


}





