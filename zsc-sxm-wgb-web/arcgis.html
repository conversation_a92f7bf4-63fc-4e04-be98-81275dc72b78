<!DOCTYPE html>
<html>

<head>
	<meta charset="utf-8">
	<meta name="viewport"
		content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
	<title>微格</title>

</head>

<body>

	<script>
		red()
		function red(){
				function getUrlParam(name){
					var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
					var r = window.location.search.substr(1).match(reg);
					if (r!=null) return unescape(r[2]); return null;
				}
				let url=window.location.href
				if(url.indexOf('token')==-1){
					alert('验证失败')
					return
				}
				let token = getUrlParam('token');
				let source = getUrlParam('source')
				localStorage.setItem('source',source);
			    localStorage.setItem('token',token);
			    window.location.replace("/micro_grid_app/arcgis.html")
			}
	</script>
</body>

</html>
